# 好习惯小帮手 - 儿童习惯追踪与奖励系统

这是一个专为7岁儿童设计的习惯追踪和奖励系统微信小程序，通过积分机制鼓励孩子养成良好习惯。

## 功能特色

### 🌟 核心功能
- **习惯管理**：添加、编辑和删除日常习惯
- **多次完成**：支持同一习惯每日多次完成，累积积分
- **完成撤销**：支持撤销当日最后一次完成记录
- **积分系统**：完成习惯获得积分，可自定义积分值
- **奖励管理**：添加、编辑和删除奖励项目
- **奖励兑换**：用积分兑换预设的奖励
- **每日记录**：查看当日完成情况和积分变化日志
- **进度追踪**：可视化显示每日习惯完成进度

### 🎨 儿童友好设计
- **可爱界面**：粉色主题，适合儿童审美
- **紧凑布局**：优化的界面结构，信息密度适中
- **实时反馈**：积分变化立即显示，操作结果即时可见
- **简单操作**：大按钮，易于点击
- **即时反馈**：完成习惯时有庆祝动画和震动
- **鼓励性语言**：正面的提示和庆祝信息

### 📱 技术特点
- **本地存储**：使用微信小程序本地存储，无需网络
- **实时更新**：页面间数据实时同步
- **响应式设计**：适配不同屏幕尺寸

## 使用说明

### 首次使用
1. 打开小程序，系统会自动初始化示例数据
2. 包含4个示例习惯：刷牙、整理玩具、读书15分钟、帮助家人
3. 包含4个示例奖励：看动画片、买小玩具、去公园玩、吃冰淇淋

### 日常使用流程
1. **查看今日任务**：在首页或"今日记录"页面查看当天需要完成的习惯
2. **完成习惯**：在"我的习惯"页面点击"完成"按钮，支持多次完成
3. **撤销完成**：如果误操作，可以点击"撤销"按钮撤销最后一次完成
4. **获得积分**：完成习惯后立即获得相应积分和庆祝反馈
5. **兑换奖励**：在"我的奖励"页面用积分兑换喜欢的奖励
6. **查看记录**：在"今日记录"页面查看当天的完成情况和积分变化

### 管理功能
- **添加习惯**：在习惯页面点击"添加新习惯"，设置名称和积分值
- **编辑习惯**：点击"编辑"按钮修改习惯名称和积分值
- **删除习惯**：点击"删除"按钮移除不需要的习惯
- **添加奖励**：在奖励页面点击"添加新奖励"，设置名称和所需积分
- **编辑奖励**：点击"编辑"按钮修改奖励名称和所需积分
- **删除奖励**：点击"删除"按钮移除不需要的奖励

## 数据存储

所有数据存储在本地，包括：
- `habits`：习惯列表，每个习惯包含完成记录数组（支持多次完成）
- `rewards`：奖励列表
- `totalPoints`：当前总积分
- `pointsLogs`：积分变化日志
- `redemptions`：兑换历史记录

## 技术栈

- **框架**：微信小程序原生开发
- **存储**：微信小程序本地存储API
- **UI**：自定义CSS样式，响应式设计
- **交互**：触摸反馈、震动反馈、动画效果

## 开发说明

### 项目结构
```
miniprogram/
├── pages/
│   ├── index/           # 首页
│   ├── habits/list/     # 习惯列表页
│   ├── rewards/list     # 奖励列表页
│   └── daily/view       # 每日记录页
├── app.js              # 应用入口，包含示例数据初始化
├── app.json            # 应用配置
└── app.wxss            # 全局样式
```

### 主要页面功能
- **index**：显示总积分，提供导航入口
- **habits/list**：管理习惯，标记完成状态
- **rewards/list**：管理奖励，进行积分兑换
- **daily/view**：显示当日进度和积分日志

## 未来改进方向

- [ ] 添加周/月统计图表
- [ ] 增加习惯连续完成天数记录
- [ ] 添加家长监督功能
- [ ] 支持多个孩子的数据分离
- [ ] 添加云端数据同步
- [ ] 增加更多庆祝动画和音效

