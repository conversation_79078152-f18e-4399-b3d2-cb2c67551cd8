// app.js
App({
  onLaunch: function () {
    this.globalData = {
      // env 参数说明：
      //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
      //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
      //   如不填则使用默认环境（第一个创建的环境）
      env: ""
    };

    // 初始化示例数据
    this.initSampleData();

    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      wx.cloud.init({
        env: this.globalData.env,
        traceUser: true,
      });
    }
  },

  // 初始化示例数据
  initSampleData() {
    // 检查是否已经初始化过
    const hasInitialized = wx.getStorageSync('hasInitialized');
    if (hasInitialized) {
      return;
    }

    // 初始化示例习惯
    const sampleHabits = [
      {
        id: Date.now() + 1,
        name: '刷牙',
        points: 2,
        completionRecords: []
      },
      {
        id: Date.now() + 2,
        name: '整理玩具',
        points: 3,
        completionRecords: []
      },
      {
        id: Date.now() + 3,
        name: '读书15分钟',
        points: 5,
        completionRecords: []
      },
      {
        id: Date.now() + 4,
        name: '帮助家人',
        points: 4,
        completionRecords: []
      }
    ];

    // 初始化示例奖励
    const sampleRewards = [
      {
        id: Date.now() + 10,
        name: '看动画片30分钟',
        points: 10
      },
      {
        id: Date.now() + 11,
        name: '买一个小玩具',
        points: 20
      },
      {
        id: Date.now() + 12,
        name: '去公园玩',
        points: 30
      },
      {
        id: Date.now() + 13,
        name: '吃冰淇淋',
        points: 15
      }
    ];

    // 保存到本地存储
    wx.setStorageSync('habits', sampleHabits);
    wx.setStorageSync('rewards', sampleRewards);
    wx.setStorageSync('totalPoints', 0);
    wx.setStorageSync('pointsLogs', []);
    wx.setStorageSync('redemptions', []);
    wx.setStorageSync('hasInitialized', true);

    console.log('示例数据初始化完成');
  }
});
