
/* pages/habits/list/index.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #FFF9FA;
}

.header {
  text-align: center;
  font-size: 40rpx;
  font-weight: bold;
  color: #FF69B4;
  margin: 30rpx 0;
}

.habit-list {
  height: 70vh;
}

.habit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.1);
  transition: all 0.3s ease;
}

.habit-item:active {
  transform: scale(0.98);
}

.habit-item.completed {
  opacity: 0.6;
}

.habit-info {
  display: flex;
  align-items: center;
}

.habit-name {
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #333;
}

.habit-points {
  margin-left: 20rpx;
  color: #FF69B4;
  font-weight: bold;
}

.delete-btn {
  background-color: #FF6B81;
  color: white;
  border-radius: 40rpx;
  font-size: 24rpx;
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  transition: all 0.3s ease;
}

.delete-btn:active {
  background-color: #FF4757;
}

.add-btn {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  background-color: #FF69B4;
  color: white;
  border-radius: 40rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
}

.add-btn:active {
  background-color: #FF1493;
  transform: translateX(-50%) scale(0.98);
}

.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

.dialog-content {
  width: 80%;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.dialog-header {
  padding: 30rpx;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  background-color: #FFC0CB;
  color: white;
}

.dialog-body {
  padding: 40rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-group text {
  display: block;
  margin-bottom: 10rpx;
  color: #FF69B4;
  font-size: 28rpx;
}

.input-group input {
  border: 2rpx solid #FFC0CB;
  padding: 20rpx;
  border-radius: 10rpx;
  width: 100%;
  font-size: 28rpx;
}

.dialog-footer {
  display: flex;
  border-top: 2rpx solid #f5f5f5;
}

.dialog-footer button {
  flex: 1;
  border-radius: 0;
  background-color: white;
  color: #FF69B4;
  border: none;
  font-size: 30rpx;
  height: 90rpx;
  line-height: 90rpx;
}

.dialog-footer button::after {
  border: none;
}

.dialog-footer .confirm-btn {
  color: white;
  background-color: #FF69B4;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(50rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}