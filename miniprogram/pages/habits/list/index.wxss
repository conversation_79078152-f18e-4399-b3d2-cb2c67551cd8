
/* pages/habits/list/index.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #FFF9FA;
}

.header {
  margin: 20rpx 0;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.2);
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.points-display {
  text-align: right;
}

.points-label {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4rpx;
}

.points-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.habit-list {
  height: 70vh;
}

.habit-item {
  padding: 15rpx;
  margin-bottom: 10rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 105, 180, 0.08);
  transition: all 0.3s ease;
}

.habit-item:active {
  transform: scale(0.98);
}

.habit-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.habit-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.habit-checkbox {
  margin-right: 20rpx;
}

.checkbox {
  width: 50rpx;
  height: 50rpx;
  border: 3rpx solid #FF69B4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background-color: #FF69B4;
  border-color: #FF69B4;
}

.checkmark {
  color: white;
  font-size: 30rpx;
  font-weight: bold;
}

.habit-details {
  flex: 1;
}

.habit-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.habit-points {
  color: #FF69B4;
  font-size: 22rpx;
  font-weight: bold;
  background: rgba(255, 105, 180, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.habit-status {
  color: #4CAF50;
  font-size: 20rpx;
  font-weight: bold;
  background: rgba(76, 175, 80, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.habit-actions-row {
  display: flex;
  gap: 8rpx;
}

/* 统一按钮样式 */
.btn {
  border: none;
  border-radius: 16rpx;
  padding: 8rpx 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 60rpx;
  height: 56rpx;
  line-height: 40rpx;
}

.btn:active {
  transform: scale(0.95);
}

.complete-btn {
  flex: 2;
  background: #4CAF50;
  color: white;
  box-shadow: 0 1rpx 4rpx rgba(76, 175, 80, 0.3);
}

.edit-btn {
  flex: 1;
  background: #2196F3;
  color: white;
}

.delete-btn {
  flex: 1;
  background: #FF6B81;
  color: white;
}

/* 积分变化动画 */
.points-change-animation {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  opacity: 0;
  transition: all 0.3s ease;
}

.points-change-animation.show {
  opacity: 1;
  animation: pointsFloat 2s ease-out;
}

.points-change-text {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  background: rgba(255, 255, 255, 0.9);
}

.points-change-text.gain {
  color: #4CAF50;
  border: 3rpx solid #4CAF50;
}

.points-change-text.loss {
  color: #FF6B6B;
  border: 3rpx solid #FF6B6B;
}

@keyframes pointsFloat {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  20% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  80% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -80%) scale(0.8);
    opacity: 0;
  }
}

.add-btn {
  position: fixed;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 80rpx;
  background-color: #FF69B4;
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 3rpx 12rpx rgba(255, 105, 180, 0.3);
  transition: all 0.2s ease;
}

.add-btn:active {
  background-color: #FF1493;
  transform: translateX(-50%) scale(0.95);
}

.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

.dialog-content {
  width: 80%;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.dialog-header {
  padding: 30rpx;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  background-color: #FFC0CB;
  color: white;
}

.dialog-body {
  padding: 40rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-group text {
  display: block;
  margin-bottom: 10rpx;
  color: #FF69B4;
  font-size: 28rpx;
}

.input-group input {
  border: 2rpx solid #FFC0CB;
  padding: 20rpx;
  border-radius: 10rpx;
  width: 100%;
  font-size: 28rpx;
}

.dialog-footer {
  display: flex;
  border-top: 2rpx solid #f5f5f5;
}

.dialog-footer button {
  flex: 1;
  border-radius: 0;
  background-color: white;
  color: #FF69B4;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  height: 80rpx;
  line-height: 80rpx;
}

.dialog-footer button::after {
  border: none;
}

.dialog-footer .confirm-btn {
  color: white;
  background-color: #FF69B4;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(50rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}