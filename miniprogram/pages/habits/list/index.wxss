
/* pages/habits/list/index.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #FFF9FA;
}

.header {
  margin: 20rpx 0;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.2);
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.points-display {
  text-align: right;
}

.points-label {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4rpx;
}

.points-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.habit-list {
  height: 70vh;
}

.habit-item {
  padding: 20rpx;
  margin-bottom: 15rpx;
  background-color: white;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 105, 180, 0.1);
  transition: all 0.3s ease;
}

.habit-item:active {
  transform: scale(0.98);
}

.habit-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.habit-main-info {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.habit-checkbox {
  margin-right: 20rpx;
}

.checkbox {
  width: 50rpx;
  height: 50rpx;
  border: 3rpx solid #FF69B4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background-color: #FF69B4;
  border-color: #FF69B4;
}

.checkmark {
  color: white;
  font-size: 30rpx;
  font-weight: bold;
}

.habit-details {
  flex: 1;
}

.habit-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.habit-points {
  color: #FF69B4;
  font-size: 24rpx;
  font-weight: bold;
}

.habit-status {
  color: #4CAF50;
  font-size: 22rpx;
  font-weight: bold;
}

.habit-actions-row {
  display: flex;
  gap: 10rpx;
}

.complete-btn {
  flex: 2;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.complete-btn:active {
  transform: scale(0.95);
}

.undo-btn {
  flex: 1;
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.undo-btn:active {
  transform: scale(0.95);
}

.edit-btn {
  flex: 1;
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.edit-btn:active {
  transform: scale(0.95);
}

.delete-btn {
  flex: 1;
  background-color: #FF6B81;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.delete-btn:active {
  transform: scale(0.95);
}

.add-btn {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  background-color: #FF69B4;
  color: white;
  border-radius: 40rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
}

.add-btn:active {
  background-color: #FF1493;
  transform: translateX(-50%) scale(0.98);
}

.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

.dialog-content {
  width: 80%;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.dialog-header {
  padding: 30rpx;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  background-color: #FFC0CB;
  color: white;
}

.dialog-body {
  padding: 40rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-group text {
  display: block;
  margin-bottom: 10rpx;
  color: #FF69B4;
  font-size: 28rpx;
}

.input-group input {
  border: 2rpx solid #FFC0CB;
  padding: 20rpx;
  border-radius: 10rpx;
  width: 100%;
  font-size: 28rpx;
}

.dialog-footer {
  display: flex;
  border-top: 2rpx solid #f5f5f5;
}

.dialog-footer button {
  flex: 1;
  border-radius: 0;
  background-color: white;
  color: #FF69B4;
  border: none;
  font-size: 30rpx;
  height: 90rpx;
  line-height: 90rpx;
}

.dialog-footer button::after {
  border: none;
}

.dialog-footer .confirm-btn {
  color: white;
  background-color: #FF69B4;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(50rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}