
// pages/habits/list/index.js
Page({
  data: {
    habits: [], // 习惯列表
    totalPoints: 0, // 当前总积分
    showAddDialog: false, // 是否显示添加对话框
    showEditDialog: false, // 是否显示编辑对话框
    newHabitName: '', // 新习惯名称
    newHabitPoints: 1, // 新习惯积分值
    editingHabit: null, // 正在编辑的习惯
    editHabitName: '', // 编辑习惯名称
    editHabitPoints: 1, // 编辑习惯积分值
    showPointsChange: false, // 是否显示积分变化动画
    pointsChangeText: '', // 积分变化文本
    pointsChangeType: '' // 积分变化类型 (gain/loss)
  },

  // 加载习惯列表
  onLoad() {
    this.loadData();
  },

  onShow() {
    this.loadData();
  },

  // 加载所有数据
  loadData() {
    this.loadHabits();
    this.loadTotalPoints();
  },

  // 加载总积分
  loadTotalPoints() {
    const totalPoints = wx.getStorageSync('totalPoints') || 0;
    this.setData({ totalPoints });
  },

  // 从本地存储加载习惯
  loadHabits() {
    const habits = wx.getStorageSync('habits') || [];
    const today = new Date().toDateString();

    // 为每个习惯添加今日完成状态和完成次数
    const habitsWithStatus = habits.map(habit => {
      const todayCompletions = this.getTodayCompletions(habit, today);
      return {
        ...habit,
        completedToday: todayCompletions > 0,
        todayCompletions: todayCompletions
      };
    });

    this.setData({ habits: habitsWithStatus });
  },

  // 获取今日完成次数
  getTodayCompletions(habit, today) {
    if (!habit.completionRecords) return 0;
    return habit.completionRecords.filter(record =>
      new Date(record.date).toDateString() === today
    ).length;
  },

  // 显示添加习惯对话框
  showAddHabitDialog() {
    this.setData({ showAddDialog: true });
  },

  // 隐藏添加习惯对话框
  hideAddHabitDialog() {
    this.setData({
      showAddDialog: false,
      newHabitName: '',
      newHabitPoints: 1
    });
  },

  // 显示编辑习惯对话框
  showEditHabitDialog(e) {
    const { id } = e.currentTarget.dataset;
    const habit = this.data.habits.find(h => h.id === id);

    if (habit) {
      this.setData({
        showEditDialog: true,
        editingHabit: habit,
        editHabitName: habit.name,
        editHabitPoints: habit.points
      });
    }
  },

  // 隐藏编辑习惯对话框
  hideEditHabitDialog() {
    this.setData({
      showEditDialog: false,
      editingHabit: null,
      editHabitName: '',
      editHabitPoints: 1
    });
  },

  // 输入新习惯名称
  inputHabitName(e) {
    this.setData({ newHabitName: e.detail.value });
  },

  // 输入新习惯积分
  inputHabitPoints(e) {
    this.setData({ newHabitPoints: Number(e.detail.value) || 1 });
  },

  // 输入编辑习惯名称
  inputEditHabitName(e) {
    this.setData({ editHabitName: e.detail.value });
  },

  // 输入编辑习惯积分
  inputEditHabitPoints(e) {
    this.setData({ editHabitPoints: Number(e.detail.value) || 1 });
  },

  // 添加新习惯
  addHabit() {
    if (!this.data.newHabitName.trim()) {
      wx.showToast({
        title: '请输入习惯名称',
        icon: 'none'
      });
      return;
    }

    const newHabit = {
      id: Date.now(),
      name: this.data.newHabitName.trim(),
      points: this.data.newHabitPoints,
      completionRecords: []
    };

    const habits = [...this.data.habits, newHabit];
    wx.setStorageSync('habits', habits);
    this.loadHabits();
    this.hideAddHabitDialog();
  },

  // 保存编辑的习惯
  saveEditHabit() {
    if (!this.data.editHabitName.trim()) {
      wx.showToast({
        title: '请输入习惯名称',
        icon: 'none'
      });
      return;
    }

    const habits = wx.getStorageSync('habits') || [];
    const updatedHabits = habits.map(habit => {
      if (habit.id === this.data.editingHabit.id) {
        return {
          ...habit,
          name: this.data.editHabitName.trim(),
          points: this.data.editHabitPoints
        };
      }
      return habit;
    });

    wx.setStorageSync('habits', updatedHabits);
    this.loadHabits();
    this.hideEditHabitDialog();

    wx.showToast({
      title: '修改成功',
      icon: 'success'
    });
  },

  // 删除习惯
  deleteHabit(e) {
    const { id } = e.currentTarget.dataset;
    const habits = this.data.habits.filter(habit => habit.id !== id);
    wx.setStorageSync('habits', habits);
    this.setData({ habits });
  },

  // 完成习惯（支持多次完成）
  completeHabit(e) {
    const { id } = e.currentTarget.dataset;
    const habit = this.data.habits.find(h => h.id === id);

    if (!habit) return;

    // 添加完成记录
    this.addCompletionRecord(id);
    this.updatePoints(habit.points);
    this.logPointsChange(habit.name, habit.points, '完成习惯');

    // 庆祝效果
    this.showCelebration(habit);

    this.loadHabits(); // 重新加载以更新显示
  },



  // 添加完成记录
  addCompletionRecord(habitId) {
    const habits = wx.getStorageSync('habits') || [];
    const updatedHabits = habits.map(habit => {
      if (habit.id === habitId) {
        const completionRecords = habit.completionRecords || [];
        completionRecords.push({
          id: Date.now(),
          date: new Date().toISOString()
        });
        return { ...habit, completionRecords };
      }
      return habit;
    });
    wx.setStorageSync('habits', updatedHabits);
  },

  // 移除最后一次完成记录
  removeLastCompletionRecord(habitId) {
    const habits = wx.getStorageSync('habits') || [];
    const today = new Date().toDateString();

    const updatedHabits = habits.map(habit => {
      if (habit.id === habitId) {
        const completionRecords = habit.completionRecords || [];
        // 找到今天的完成记录并移除最后一个
        const todayRecords = completionRecords.filter(record =>
          new Date(record.date).toDateString() === today
        );

        if (todayRecords.length > 0) {
          const lastRecord = todayRecords[todayRecords.length - 1];
          const filteredRecords = completionRecords.filter(record =>
            record.id !== lastRecord.id
          );
          return { ...habit, completionRecords: filteredRecords };
        }
      }
      return habit;
    });
    wx.setStorageSync('habits', updatedHabits);
  },

  // 更新积分
  updatePoints(points) {
    const currentPoints = wx.getStorageSync('totalPoints') || 0;
    const newPoints = Math.max(0, currentPoints + points);
    wx.setStorageSync('totalPoints', newPoints);
    this.setData({ totalPoints: newPoints }); // 实时更新界面显示

    // 显示积分变化动画
    this.showPointsChangeAnimation(points);
  },

  // 显示积分变化动画
  showPointsChangeAnimation(points) {
    const changeText = points > 0 ? `+${points}` : `${points}`;
    const changeType = points > 0 ? 'gain' : 'loss';

    this.setData({
      showPointsChange: true,
      pointsChangeText: changeText,
      pointsChangeType: changeType
    });

    // 2秒后隐藏动画
    setTimeout(() => {
      this.setData({ showPointsChange: false });
    }, 2000);
  },

  // 记录积分变化日志
  logPointsChange(habitName, points, action) {
    const logs = wx.getStorageSync('pointsLogs') || [];
    const newLog = {
      id: Date.now(),
      date: new Date().toISOString(),
      habitName,
      points,
      action,
      type: points > 0 ? 'gain' : 'loss'
    };
    logs.unshift(newLog); // 添加到开头
    // 只保留最近100条记录
    if (logs.length > 100) {
      logs.splice(100);
    }
    wx.setStorageSync('pointsLogs', logs);
  },

  // 检查今天是否已完成
  isCompletedToday(habit) {
    const today = new Date().toDateString();
    return habit.completedDates && habit.completedDates.includes(today);
  },

  // 显示庆祝效果
  showCelebration(habit) {
    const celebrations = [
      `太棒了！完成了"${habit.name}"，获得${habit.points}积分！🎉`,
      `好厉害！"${habit.name}"完成啦，+${habit.points}积分！⭐`,
      `真棒！坚持"${habit.name}"，奖励${habit.points}积分！🌟`,
      `太好了！"${habit.name}"做得很好，${habit.points}积分到手！🎊`
    ];

    const randomMessage = celebrations[Math.floor(Math.random() * celebrations.length)];

    wx.showToast({
      title: randomMessage,
      icon: 'success',
      duration: 2000
    });

    // 震动反馈
    wx.vibrateShort({
      type: 'light'
    });
  }
});