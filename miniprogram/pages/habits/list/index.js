
// pages/habits/list/index.js
Page({
  data: {
    habits: [], // 习惯列表
    showAddDialog: false, // 是否显示添加对话框
    newHabitName: '', // 新习惯名称
    newHabitPoints: 1 // 新习惯积分值
  },

  // 加载习惯列表
  onLoad() {
    this.loadHabits();
  },

  // 从本地存储加载习惯
  loadHabits() {
    const habits = wx.getStorageSync('habits') || [];
    const today = new Date().toDateString();

    // 为每个习惯添加今日完成状态
    const habitsWithStatus = habits.map(habit => ({
      ...habit,
      completedToday: habit.completedDates && habit.completedDates.includes(today)
    }));

    this.setData({ habits: habitsWithStatus });
  },

  // 显示添加习惯对话框
  showAddHabitDialog() {
    this.setData({ showAddDialog: true });
  },

  // 隐藏添加习惯对话框
  hideAddHabitDialog() {
    this.setData({ 
      showAddDialog: false,
      newHabitName: '',
      newHabitPoints: 1
    });
  },

  // 输入新习惯名称
  inputHabitName(e) {
    this.setData({ newHabitName: e.detail.value });
  },

  // 输入新习惯积分
  inputHabitPoints(e) {
    this.setData({ newHabitPoints: Number(e.detail.value) || 1 });
  },

  // 添加新习惯
  addHabit() {
    if (!this.data.newHabitName.trim()) {
      wx.showToast({
        title: '请输入习惯名称',
        icon: 'none'
      });
      return;
    }

    const newHabit = {
      id: Date.now(),
      name: this.data.newHabitName.trim(),
      points: this.data.newHabitPoints,
      completed: false
    };

    const habits = [...this.data.habits, newHabit];
    wx.setStorageSync('habits', habits);
    this.setData({ habits });
    this.hideAddHabitDialog();
  },

  // 删除习惯
  deleteHabit(e) {
    const { id } = e.currentTarget.dataset;
    const habits = this.data.habits.filter(habit => habit.id !== id);
    wx.setStorageSync('habits', habits);
    this.setData({ habits });
  },

  // 切换习惯完成状态
  toggleHabit(e) {
    const { id } = e.currentTarget.dataset;
    const habit = this.data.habits.find(h => h.id === id);

    if (!habit) return;

    const today = new Date().toDateString();
    const completedToday = habit.completedDates && habit.completedDates.includes(today);

    if (completedToday) {
      // 取消完成 - 扣除积分
      this.updatePoints(-habit.points);
      this.removeCompletedDate(id, today);
      wx.showToast({
        title: `取消完成，扣除${habit.points}积分`,
        icon: 'none'
      });
    } else {
      // 完成习惯 - 增加积分
      this.updatePoints(habit.points);
      this.addCompletedDate(id, today);
      this.logPointsChange(habit.name, habit.points, '完成习惯');

      // 庆祝效果
      this.showCelebration(habit);
    }

    this.loadHabits(); // 重新加载以更新显示
  },

  // 添加完成日期
  addCompletedDate(habitId, date) {
    const habits = this.data.habits.map(habit => {
      if (habit.id === habitId) {
        const completedDates = habit.completedDates || [];
        if (!completedDates.includes(date)) {
          completedDates.push(date);
        }
        return { ...habit, completedDates };
      }
      return habit;
    });
    wx.setStorageSync('habits', habits);
  },

  // 移除完成日期
  removeCompletedDate(habitId, date) {
    const habits = this.data.habits.map(habit => {
      if (habit.id === habitId) {
        const completedDates = (habit.completedDates || []).filter(d => d !== date);
        return { ...habit, completedDates };
      }
      return habit;
    });
    wx.setStorageSync('habits', habits);
  },

  // 更新积分
  updatePoints(points) {
    const currentPoints = wx.getStorageSync('totalPoints') || 0;
    const newPoints = Math.max(0, currentPoints + points);
    wx.setStorageSync('totalPoints', newPoints);
  },

  // 记录积分变化日志
  logPointsChange(habitName, points, action) {
    const logs = wx.getStorageSync('pointsLogs') || [];
    const newLog = {
      id: Date.now(),
      date: new Date().toISOString(),
      habitName,
      points,
      action,
      type: points > 0 ? 'gain' : 'loss'
    };
    logs.unshift(newLog); // 添加到开头
    // 只保留最近100条记录
    if (logs.length > 100) {
      logs.splice(100);
    }
    wx.setStorageSync('pointsLogs', logs);
  },

  // 检查今天是否已完成
  isCompletedToday(habit) {
    const today = new Date().toDateString();
    return habit.completedDates && habit.completedDates.includes(today);
  },

  // 显示庆祝效果
  showCelebration(habit) {
    const celebrations = [
      `太棒了！完成了"${habit.name}"，获得${habit.points}积分！🎉`,
      `好厉害！"${habit.name}"完成啦，+${habit.points}积分！⭐`,
      `真棒！坚持"${habit.name}"，奖励${habit.points}积分！🌟`,
      `太好了！"${habit.name}"做得很好，${habit.points}积分到手！🎊`
    ];

    const randomMessage = celebrations[Math.floor(Math.random() * celebrations.length)];

    wx.showToast({
      title: randomMessage,
      icon: 'success',
      duration: 2000
    });

    // 震动反馈
    wx.vibrateShort({
      type: 'light'
    });
  }
});