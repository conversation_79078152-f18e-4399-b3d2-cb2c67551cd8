
// pages/habits/list/index.js
Page({
  data: {
    habits: [], // 习惯列表
    showAddDialog: false, // 是否显示添加对话框
    newHabitName: '', // 新习惯名称
    newHabitPoints: 1 // 新习惯积分值
  },

  // 加载习惯列表
  onLoad() {
    this.loadHabits();
  },

  // 从本地存储加载习惯
  loadHabits() {
    const habits = wx.getStorageSync('habits') || [];
    this.setData({ habits });
  },

  // 显示添加习惯对话框
  showAddHabitDialog() {
    this.setData({ showAddDialog: true });
  },

  // 隐藏添加习惯对话框
  hideAddHabitDialog() {
    this.setData({ 
      showAddDialog: false,
      newHabitName: '',
      newHabitPoints: 1
    });
  },

  // 输入新习惯名称
  inputHabitName(e) {
    this.setData({ newHabitName: e.detail.value });
  },

  // 输入新习惯积分
  inputHabitPoints(e) {
    this.setData({ newHabitPoints: Number(e.detail.value) || 1 });
  },

  // 添加新习惯
  addHabit() {
    if (!this.data.newHabitName.trim()) {
      wx.showToast({
        title: '请输入习惯名称',
        icon: 'none'
      });
      return;
    }

    const newHabit = {
      id: Date.now(),
      name: this.data.newHabitName.trim(),
      points: this.data.newHabitPoints,
      completed: false
    };

    const habits = [...this.data.habits, newHabit];
    wx.setStorageSync('habits', habits);
    this.setData({ habits });
    this.hideAddHabitDialog();
  },

  // 删除习惯
  deleteHabit(e) {
    const { id } = e.currentTarget.dataset;
    const habits = this.data.habits.filter(habit => habit.id !== id);
    wx.setStorageSync('habits', habits);
    this.setData({ habits });
  },

  // 切换习惯完成状态
  toggleHabit(e) {
    const { id } = e.currentTarget.dataset;
    const habits = this.data.habits.map(habit => {
      if (habit.id === id) {
        return { ...habit, completed: !habit.completed };
      }
      return habit;
    });
    wx.setStorageSync('habits', habits);
    this.setData({ habits });
  }
});