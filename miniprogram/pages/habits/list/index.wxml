
<!--pages/habits/list/index.wxml-->
<view class="container">
  <!-- 标题 -->
  <view class="header">
    <text>我的好习惯</text>
  </view>

  <!-- 习惯列表 -->
  <scroll-view scroll-y class="habit-list">
    <block wx:for="{{habits}}" wx:key="id">
      <view class="habit-item">
        <view class="habit-info">
          <view class="habit-details">
            <text class="habit-name">{{item.name}}</text>
            <text class="habit-points">+{{item.points}}分</text>
            <text class="habit-status" wx:if="{{item.todayCompletions > 0}}">
              今日已完成 {{item.todayCompletions}} 次
            </text>
          </view>
        </view>

        <view class="habit-actions">
          <!-- 完成按钮 -->
          <button class="complete-btn" bindtap="completeHabit" data-id="{{item.id}}">
            完成 +{{item.points}}
          </button>

          <!-- 撤销按钮（仅在今日有完成记录时显示） -->
          <button
            class="undo-btn"
            bindtap="undoLastCompletion"
            data-id="{{item.id}}"
            wx:if="{{item.todayCompletions > 0}}">
            撤销
          </button>

          <!-- 编辑和删除按钮 -->
          <view class="manage-buttons">
            <button class="edit-btn" bindtap="showEditHabitDialog" data-id="{{item.id}}">编辑</button>
            <button class="delete-btn" bindtap="deleteHabit" data-id="{{item.id}}">删除</button>
          </view>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 添加按钮 -->
  <button class="add-btn" bindtap="showAddHabitDialog">添加新习惯</button>

  <!-- 添加习惯对话框 -->
  <view class="dialog-mask" wx:if="{{showAddDialog}}">
    <view class="dialog-content">
      <view class="dialog-header">
        <text>添加新习惯</text>
      </view>
      <view class="dialog-body">
        <view class="input-group">
          <text>习惯名称:</text>
          <input placeholder="请输入习惯名称" bindinput="inputHabitName" value="{{newHabitName}}"/>
        </view>
        <view class="input-group">
          <text>奖励积分:</text>
          <input type="number" placeholder="1" bindinput="inputHabitPoints" value="{{newHabitPoints}}"/>
        </view>
      </view>
      <view class="dialog-footer">
        <button class="cancel-btn" bindtap="hideAddHabitDialog">取消</button>
        <button class="confirm-btn" bindtap="addHabit">确定</button>
      </view>
    </view>
  </view>

  <!-- 编辑习惯对话框 -->
  <view class="dialog-mask" wx:if="{{showEditDialog}}">
    <view class="dialog-content">
      <view class="dialog-header">
        <text>编辑习惯</text>
      </view>
      <view class="dialog-body">
        <view class="input-group">
          <text>习惯名称:</text>
          <input placeholder="请输入习惯名称" bindinput="inputEditHabitName" value="{{editHabitName}}"/>
        </view>
        <view class="input-group">
          <text>奖励积分:</text>
          <input type="number" placeholder="1" bindinput="inputEditHabitPoints" value="{{editHabitPoints}}"/>
        </view>
      </view>
      <view class="dialog-footer">
        <button class="cancel-btn" bindtap="hideEditHabitDialog">取消</button>
        <button class="confirm-btn" bindtap="saveEditHabit">保存</button>
      </view>
    </view>
  </view>
</view>