
<!--pages/habits/list/index.wxml-->
<view class="container">
  <!-- 标题 -->
  <view class="header">
    <text>我的好习惯</text>
  </view>

  <!-- 习惯列表 -->
  <scroll-view scroll-y class="habit-list">
    <block wx:for="{{habits}}" wx:key="id">
      <view class="habit-item {{item.completed ? 'completed' : ''}}">
        <view class="habit-info">
          <checkbox checked="{{item.completed}}" bindtap="toggleHabit" data-id="{{item.id}}"/>
          <text class="habit-name">{{item.name}}</text>
          <text class="habit-points">+{{item.points}}分</text>
        </view>
        <button class="delete-btn" bindtap="deleteHabit" data-id="{{item.id}}">删除</button>
      </view>
    </block>
  </scroll-view>

  <!-- 添加按钮 -->
  <button class="add-btn" bindtap="showAddHabitDialog">添加新习惯</button>

  <!-- 添加习惯对话框 -->
  <view class="dialog-mask" wx:if="{{showAddDialog}}">
    <view class="dialog-content">
      <view class="dialog-header">
        <text>添加新习惯</text>
      </view>
      <view class="dialog-body">
        <view class="input-group">
          <text>习惯名称:</text>
          <input placeholder="请输入习惯名称" bindinput="inputHabitName" value="{{newHabitName}}"/>
        </view>
        <view class="input-group">
          <text>奖励积分:</text>
          <input type="number" placeholder="1" bindinput="inputHabitPoints" value="{{newHabitPoints}}"/>
        </view>
      </view>
      <view class="dialog-footer">
        <button class="cancel-btn" bindtap="hideAddHabitDialog">取消</button>
        <button class="confirm-btn" bindtap="addHabit">确定</button>
      </view>
    </view>
  </view>
</view>