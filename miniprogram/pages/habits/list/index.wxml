
<!--pages/habits/list/index.wxml-->
<view class="container">
  <!-- 标题和积分显示 -->
  <view class="header">
    <view class="title-section">
      <text class="page-title">我的好习惯</text>
      <view class="points-display">
        <text class="points-label">当前积分</text>
        <text class="points-value">{{totalPoints}}</text>
      </view>
    </view>
  </view>

  <!-- 习惯列表 -->
  <scroll-view scroll-y class="habit-list">
    <block wx:for="{{habits}}" wx:key="id">
      <view class="habit-item">
        <!-- 习惯信息 -->
        <view class="habit-info-row">
          <text class="habit-name">{{item.name}}</text>
          <view class="habit-meta">
            <text class="habit-points">+{{item.points}}</text>
            <text class="habit-status" wx:if="{{item.todayCompletions > 0}}">×{{item.todayCompletions}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="habit-actions-row">
          <button class="btn complete-btn" bindtap="completeHabit" data-id="{{item.id}}">
            +{{item.points}}
          </button>

          <button
            class="btn undo-btn"
            bindtap="undoLastCompletion"
            data-id="{{item.id}}"
            wx:if="{{item.todayCompletions > 0}}">
            撤销
          </button>

          <button class="btn edit-btn" bindtap="showEditHabitDialog" data-id="{{item.id}}">编辑</button>
          <button class="btn delete-btn" bindtap="deleteHabit" data-id="{{item.id}}">删除</button>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 积分变化动画 -->
  <view class="points-change-animation {{showPointsChange ? 'show' : ''}}" wx:if="{{showPointsChange}}">
    <text class="points-change-text {{pointsChangeType}}">{{pointsChangeText}}</text>
  </view>

  <!-- 添加按钮 -->
  <button class="add-btn" bindtap="showAddHabitDialog">添加新习惯</button>

  <!-- 添加习惯对话框 -->
  <view class="dialog-mask" wx:if="{{showAddDialog}}">
    <view class="dialog-content">
      <view class="dialog-header">
        <text>添加新习惯</text>
      </view>
      <view class="dialog-body">
        <view class="input-group">
          <text>习惯名称:</text>
          <input placeholder="请输入习惯名称" bindinput="inputHabitName" value="{{newHabitName}}"/>
        </view>
        <view class="input-group">
          <text>奖励积分:</text>
          <input type="number" placeholder="1" bindinput="inputHabitPoints" value="{{newHabitPoints}}"/>
        </view>
      </view>
      <view class="dialog-footer">
        <button class="cancel-btn" bindtap="hideAddHabitDialog">取消</button>
        <button class="confirm-btn" bindtap="addHabit">确定</button>
      </view>
    </view>
  </view>

  <!-- 编辑习惯对话框 -->
  <view class="dialog-mask" wx:if="{{showEditDialog}}">
    <view class="dialog-content">
      <view class="dialog-header">
        <text>编辑习惯</text>
      </view>
      <view class="dialog-body">
        <view class="input-group">
          <text>习惯名称:</text>
          <input placeholder="请输入习惯名称" bindinput="inputEditHabitName" value="{{editHabitName}}"/>
        </view>
        <view class="input-group">
          <text>奖励积分:</text>
          <input type="number" placeholder="1" bindinput="inputEditHabitPoints" value="{{editHabitPoints}}"/>
        </view>
      </view>
      <view class="dialog-footer">
        <button class="cancel-btn" bindtap="hideEditHabitDialog">取消</button>
        <button class="confirm-btn" bindtap="saveEditHabit">保存</button>
      </view>
    </view>
  </view>
</view>