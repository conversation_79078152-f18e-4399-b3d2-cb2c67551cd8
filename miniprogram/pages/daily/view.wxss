/* pages/daily/view.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #FFF9FA;
}

.header {
  text-align: center;
  margin: 30rpx 0;
}

.date-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF69B4;
}

.overview-card {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.1);
}

.points-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.points-label {
  font-size: 28rpx;
  color: #666;
}

.points-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
}

.progress-section {
  text-align: center;
}

.progress-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.progress-bar {
  width: 100%;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #999;
}

.quick-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  padding: 30rpx 20rpx;
  background-color: white;
  border-radius: 20rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.action-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  font-size: 40rpx;
}

.btn-text {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

.habits-btn {
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
}

.rewards-btn {
  background: linear-gradient(135deg, #E6F3FF, #F0F8FF);
}

.logs-section {
  flex: 1;
}

.section-title {
  margin-bottom: 20rpx;
}

.section-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.logs-list {
  height: 40vh;
}

.log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background-color: white;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.log-item.gain {
  border-left: 6rpx solid #4CAF50;
}

.log-item.loss {
  border-left: 6rpx solid #FF6B6B;
}

.log-info {
  flex: 1;
}

.log-habit {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.log-action {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.log-time {
  display: block;
  font-size: 22rpx;
  color: #999;
}

.log-points {
  font-size: 32rpx;
  font-weight: bold;
}

.log-points.positive {
  color: #4CAF50;
}

.log-points.negative {
  color: #FF6B6B;
}

.empty-logs {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.empty-hint {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}