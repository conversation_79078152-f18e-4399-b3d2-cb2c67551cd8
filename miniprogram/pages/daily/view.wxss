/* pages/daily/view.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #FFF9FA;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

.header {
  text-align: center;
  margin: 20rpx 0;
  width: 100%;
}

.date-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF69B4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.overview-card {
  background-color: white;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.points-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
}

.points-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.points-value {
  font-size: 42rpx;
  font-weight: bold;
  color: #FF69B4;
  flex-shrink: 0;
}

.progress-section {
  text-align: center;
  width: 100%;
}

.progress-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
  box-sizing: border-box;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 8rpx;
  transition: width 0.3s ease;
  min-width: 0;
}

.progress-text {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
}

.quick-actions {
  display: flex;
  gap: 12rpx;
  margin-bottom: 25rpx;
  width: 100%;
  box-sizing: border-box;
}

.action-btn {
  flex: 1;
  padding: 20rpx 10rpx;
  background-color: white;
  border-radius: 15rpx;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(255, 105, 180, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6rpx;
  min-width: 0;
  box-sizing: border-box;
}

.action-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  font-size: 28rpx;
  line-height: 1;
}

.btn-text {
  font-size: 20rpx;
  color: #333;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.habits-btn {
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
}

.rewards-btn {
  background: linear-gradient(135deg, #E6F3FF, #F0F8FF);
}

.history-btn {
  background: linear-gradient(135deg, #F0FFF0, #F5FFFA);
}

.logs-section {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  margin-bottom: 15rpx;
  width: 100%;
}

.section-title text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.logs-list {
  height: 35vh;
  width: 100%;
  box-sizing: border-box;
}

.log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx;
  margin-bottom: 8rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
}

.log-item.gain {
  border-left: 4rpx solid #4CAF50;
}

.log-item.loss {
  border-left: 4rpx solid #FF6B6B;
}

.log-info {
  flex: 1;
  min-width: 0;
  margin-right: 10rpx;
}

.log-habit {
  display: block;
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 3rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.log-action {
  display: block;
  font-size: 20rpx;
  color: #666;
  margin-bottom: 3rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.log-time {
  display: block;
  font-size: 18rpx;
  color: #999;
  white-space: nowrap;
}

.log-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.log-points {
  font-size: 24rpx;
  font-weight: bold;
  white-space: nowrap;
  margin-right: 5rpx;
}

.log-points.positive {
  color: #4CAF50;
}

.log-points.negative {
  color: #FF6B6B;
}

/* 统一按钮样式 */
.btn {
  border: none;
  border-radius: 10rpx;
  padding: 5rpx 8rpx;
  font-size: 16rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 45rpx;
  height: 40rpx;
  line-height: 30rpx;
  flex-shrink: 0;
}

.btn:active {
  transform: scale(0.95);
}

.undo-btn {
  background: #FF9800;
  color: white;
}

.empty-logs {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.empty-hint {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}

/* 历史记录对话框样式 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.history-dialog {
  width: 90%;
  max-width: 700rpx;
  height: 80%;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
}

.dialog-header text {
  font-size: 32rpx;
  font-weight: bold;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.history-content {
  flex: 1;
  padding: 15rpx;
  box-sizing: border-box;
  width: 100%;
}

.history-date-section {
  margin-bottom: 25rpx;
  width: 100%;
}

.history-date {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 12rpx;
  padding: 8rpx 12rpx;
  background: rgba(255, 105, 180, 0.1);
  border-radius: 12rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-logs {
  margin-left: 10rpx;
  width: calc(100% - 10rpx);
  box-sizing: border-box;
}

.history-log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx;
  margin-bottom: 6rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  width: 100%;
  box-sizing: border-box;
  min-height: 60rpx;
}

.history-log-item.gain {
  border-left: 3rpx solid #4CAF50;
}

.history-log-item.loss {
  border-left: 3rpx solid #FF6B6B;
}

.history-log-item .log-info {
  flex: 1;
  min-width: 0;
  margin-right: 8rpx;
}

.history-log-item .log-habit {
  font-size: 22rpx;
  margin-bottom: 2rpx;
}

.history-log-item .log-action {
  font-size: 18rpx;
  margin-bottom: 2rpx;
}

.history-log-item .log-time {
  font-size: 16rpx;
}

.history-log-item .log-points {
  font-size: 22rpx;
  flex-shrink: 0;
}

.empty-history {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-history .empty-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.empty-history .empty-text {
  font-size: 28rpx;
}