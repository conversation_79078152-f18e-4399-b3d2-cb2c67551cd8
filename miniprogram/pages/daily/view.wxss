/* pages/daily/view.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #FFF9FA;
}

.header {
  text-align: center;
  margin: 30rpx 0;
}

.date-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF69B4;
}

.overview-card {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.1);
}

.points-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.points-label {
  font-size: 28rpx;
  color: #666;
}

.points-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
}

.progress-section {
  text-align: center;
}

.progress-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.progress-bar {
  width: 100%;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF69B4, #FFB6C1);
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #999;
}

.quick-actions {
  display: flex;
  gap: 15rpx;
  margin-bottom: 25rpx;
}

.action-btn {
  flex: 1;
  padding: 25rpx 15rpx;
  background-color: white;
  border-radius: 15rpx;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(255, 105, 180, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.action-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 22rpx;
  color: #333;
  font-weight: 600;
}

.habits-btn {
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
}

.rewards-btn {
  background: linear-gradient(135deg, #E6F3FF, #F0F8FF);
}

.history-btn {
  background: linear-gradient(135deg, #F0FFF0, #F5FFFA);
}

.logs-section {
  flex: 1;
}

.section-title {
  margin-bottom: 20rpx;
}

.section-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.logs-list {
  height: 40vh;
}

.log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx;
  margin-bottom: 10rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.log-item.gain {
  border-left: 4rpx solid #4CAF50;
}

.log-item.loss {
  border-left: 4rpx solid #FF6B6B;
}

.log-info {
  flex: 1;
}

.log-habit {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.log-action {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.log-time {
  display: block;
  font-size: 20rpx;
  color: #999;
}

.log-actions {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.log-points {
  font-size: 28rpx;
  font-weight: bold;
}

.log-points.positive {
  color: #4CAF50;
}

.log-points.negative {
  color: #FF6B6B;
}

/* 统一按钮样式 */
.btn {
  border: none;
  border-radius: 12rpx;
  padding: 6rpx 10rpx;
  font-size: 18rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 50rpx;
  height: 48rpx;
  line-height: 36rpx;
}

.btn:active {
  transform: scale(0.95);
}

.undo-btn {
  background: #FF9800;
  color: white;
}

.empty-logs {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.empty-hint {
  display: block;
  font-size: 24rpx;
  color: #ccc;
}

/* 历史记录对话框样式 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.history-dialog {
  width: 90%;
  height: 80%;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
}

.dialog-header text {
  font-size: 32rpx;
  font-weight: bold;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.history-content {
  flex: 1;
  padding: 20rpx;
}

.history-date-section {
  margin-bottom: 30rpx;
}

.history-date {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 15rpx;
  padding: 10rpx 15rpx;
  background: rgba(255, 105, 180, 0.1);
  border-radius: 15rpx;
}

.history-logs {
  margin-left: 15rpx;
}

.history-log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx;
  margin-bottom: 8rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
}

.history-log-item.gain {
  border-left: 3rpx solid #4CAF50;
}

.history-log-item.loss {
  border-left: 3rpx solid #FF6B6B;
}

.empty-history {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-history .empty-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.empty-history .empty-text {
  font-size: 28rpx;
}