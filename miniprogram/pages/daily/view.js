// pages/daily/view.js
Page({
  data: {
    currentDate: '',
    totalPoints: 0,
    todayLogs: [],
    habits: [],
    completedHabitsCount: 0,
    totalHabitsCount: 0,
    showHistoryDialog: false,
    historyLogs: [],
    selectedDate: ''
  },

  onLoad() {
    this.loadData();
  },

  onShow() {
    this.loadData();
  },

  // 加载所有数据
  loadData() {
    this.setCurrentDate();
    this.loadTotalPoints();
    this.loadTodayLogs();
    this.loadHabits();
    this.calculateProgress();
  },

  // 设置当前日期
  setCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const weekday = weekdays[now.getDay()];

    this.setData({
      currentDate: `${year}年${month}月${day}日 ${weekday}`
    });
  },

  // 加载总积分
  loadTotalPoints() {
    const totalPoints = wx.getStorageSync('totalPoints') || 0;
    this.setData({ totalPoints });
  },

  // 加载今日日志
  loadTodayLogs() {
    const logs = wx.getStorageSync('pointsLogs') || [];
    const today = new Date().toDateString();

    const todayLogs = logs.filter(log => {
      const logDate = new Date(log.date).toDateString();
      return logDate === today;
    }).map(log => ({
      ...log,
      formattedTime: this.formatTime(log.date)
    }));

    this.setData({ todayLogs });
  },

  // 加载习惯列表
  loadHabits() {
    const habits = wx.getStorageSync('habits') || [];
    this.setData({ habits });
  },

  // 计算今日进度
  calculateProgress() {
    const today = new Date().toDateString();
    const completedHabitsCount = this.data.habits.filter(habit => {
      if (!habit.completionRecords) return false;
      return habit.completionRecords.some(record =>
        new Date(record.date).toDateString() === today
      );
    }).length;

    this.setData({
      completedHabitsCount,
      totalHabitsCount: this.data.habits.length
    });
  },

  // 格式化时间
  formatTime(dateString) {
    const date = new Date(dateString);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 跳转到习惯页面
  goToHabits() {
    wx.navigateTo({
      url: '/pages/habits/list/index'
    });
  },

  // 跳转到奖励页面
  goToRewards() {
    wx.navigateTo({
      url: '/pages/rewards/list'
    });
  },

  // 撤销完成记录
  undoCompletion(e) {
    const { logId, habitName, points } = e.currentTarget.dataset;

    wx.showModal({
      title: '确认撤销',
      content: `确定要撤销"${habitName}"的完成记录吗？将扣除${points}积分。`,
      success: (res) => {
        if (res.confirm) {
          this.removeCompletionByLogId(logId);
          this.updatePoints(-points);
          this.logPointsChange(habitName, -points, '撤销完成');

          wx.showToast({
            title: `已撤销，扣除${points}积分`,
            icon: 'none'
          });

          this.loadData(); // 重新加载数据
        }
      }
    });
  },

  // 根据日志ID移除完成记录
  removeCompletionByLogId(logId) {
    const habits = wx.getStorageSync('habits') || [];
    const logs = wx.getStorageSync('pointsLogs') || [];

    // 找到对应的日志记录
    const targetLog = logs.find(log => log.id == logId);
    if (!targetLog) return;

    // 找到对应的习惯并移除完成记录
    const updatedHabits = habits.map(habit => {
      if (habit.name === targetLog.habitName) {
        const completionRecords = habit.completionRecords || [];
        // 移除时间最接近的完成记录
        const targetTime = new Date(targetLog.date).getTime();
        let closestIndex = -1;
        let minDiff = Infinity;

        completionRecords.forEach((record, index) => {
          const diff = Math.abs(new Date(record.date).getTime() - targetTime);
          if (diff < minDiff) {
            minDiff = diff;
            closestIndex = index;
          }
        });

        if (closestIndex !== -1) {
          completionRecords.splice(closestIndex, 1);
        }

        return { ...habit, completionRecords };
      }
      return habit;
    });

    wx.setStorageSync('habits', updatedHabits);
  },

  // 更新积分
  updatePoints(points) {
    const currentPoints = wx.getStorageSync('totalPoints') || 0;
    const newPoints = Math.max(0, currentPoints + points);
    wx.setStorageSync('totalPoints', newPoints);
    this.setData({ totalPoints: newPoints });
  },

  // 记录积分变化日志
  logPointsChange(habitName, points, action) {
    const logs = wx.getStorageSync('pointsLogs') || [];
    const newLog = {
      id: Date.now(),
      date: new Date().toISOString(),
      habitName,
      points,
      action,
      type: points > 0 ? 'gain' : 'loss'
    };
    logs.unshift(newLog);
    if (logs.length > 100) {
      logs.splice(100);
    }
    wx.setStorageSync('pointsLogs', logs);
  },

  // 显示历史记录对话框
  showHistoryDialog() {
    this.setData({ showHistoryDialog: true });
    this.loadHistoryLogs();
  },

  // 隐藏历史记录对话框
  hideHistoryDialog() {
    this.setData({ showHistoryDialog: false });
  },

  // 加载历史记录
  loadHistoryLogs() {
    const logs = wx.getStorageSync('pointsLogs') || [];
    // 按日期分组
    const groupedLogs = {};

    logs.forEach(log => {
      const date = new Date(log.date).toDateString();
      if (!groupedLogs[date]) {
        groupedLogs[date] = [];
      }
      groupedLogs[date].push({
        ...log,
        formattedTime: this.formatTime(log.date)
      });
    });

    // 转换为数组并排序
    const historyLogs = Object.keys(groupedLogs)
      .sort((a, b) => new Date(b) - new Date(a))
      .map(date => ({
        date: this.formatDate(date),
        logs: groupedLogs[date]
      }));

    this.setData({ historyLogs });
  },

  // 格式化日期
  formatDate(dateString) {
    const date = new Date(dateString);
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();

    if (dateString === today) {
      return '今天';
    } else if (dateString === yesterday) {
      return '昨天';
    } else {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadData();
    wx.stopPullDownRefresh();
  }
})