// pages/daily/view.js
Page({
  data: {
    currentDate: '',
    totalPoints: 0,
    todayLogs: [],
    habits: [],
    completedHabitsCount: 0,
    totalHabitsCount: 0
  },

  onLoad() {
    this.loadData();
  },

  onShow() {
    this.loadData();
  },

  // 加载所有数据
  loadData() {
    this.setCurrentDate();
    this.loadTotalPoints();
    this.loadTodayLogs();
    this.loadHabits();
    this.calculateProgress();
  },

  // 设置当前日期
  setCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const weekday = weekdays[now.getDay()];

    this.setData({
      currentDate: `${year}年${month}月${day}日 ${weekday}`
    });
  },

  // 加载总积分
  loadTotalPoints() {
    const totalPoints = wx.getStorageSync('totalPoints') || 0;
    this.setData({ totalPoints });
  },

  // 加载今日日志
  loadTodayLogs() {
    const logs = wx.getStorageSync('pointsLogs') || [];
    const today = new Date().toDateString();

    const todayLogs = logs.filter(log => {
      const logDate = new Date(log.date).toDateString();
      return logDate === today;
    }).map(log => ({
      ...log,
      formattedTime: this.formatTime(log.date)
    }));

    this.setData({ todayLogs });
  },

  // 加载习惯列表
  loadHabits() {
    const habits = wx.getStorageSync('habits') || [];
    this.setData({ habits });
  },

  // 计算今日进度
  calculateProgress() {
    const today = new Date().toDateString();
    const completedHabitsCount = this.data.habits.filter(habit => {
      return habit.completedDates && habit.completedDates.includes(today);
    }).length;

    this.setData({
      completedHabitsCount,
      totalHabitsCount: this.data.habits.length
    });
  },

  // 格式化时间
  formatTime(dateString) {
    const date = new Date(dateString);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 跳转到习惯页面
  goToHabits() {
    wx.navigateTo({
      url: '/pages/habits/list/index'
    });
  },

  // 跳转到奖励页面
  goToRewards() {
    wx.navigateTo({
      url: '/pages/rewards/list'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadData();
    wx.stopPullDownRefresh();
  }
})