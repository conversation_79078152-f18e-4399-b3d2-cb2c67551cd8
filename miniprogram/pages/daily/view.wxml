<!--pages/daily/view.wxml-->
<view class="container">
  <!-- 日期标题 -->
  <view class="header">
    <text class="date-title">{{currentDate}}</text>
  </view>

  <!-- 今日概览 -->
  <view class="overview-card">
    <view class="points-section">
      <text class="points-label">当前积分</text>
      <text class="points-value">{{totalPoints}}</text>
    </view>

    <view class="progress-section">
      <text class="progress-label">今日进度</text>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{totalHabitsCount > 0 ? (completedHabitsCount / totalHabitsCount * 100) : 0}}%"></view>
      </view>
      <text class="progress-text">{{completedHabitsCount}}/{{totalHabitsCount}} 个习惯已完成</text>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <button class="action-btn habits-btn" bindtap="goToHabits">
      <text class="btn-icon">📝</text>
      <text class="btn-text">完成习惯</text>
    </button>
    <button class="action-btn rewards-btn" bindtap="goToRewards">
      <text class="btn-icon">🎁</text>
      <text class="btn-text">兑换奖励</text>
    </button>
  </view>

  <!-- 今日记录 -->
  <view class="logs-section">
    <view class="section-title">
      <text>今日记录</text>
    </view>

    <scroll-view scroll-y class="logs-list">
      <block wx:for="{{todayLogs}}" wx:key="id">
        <view class="log-item {{item.type === 'gain' ? 'gain' : 'loss'}}">
          <view class="log-info">
            <text class="log-habit">{{item.habitName}}</text>
            <text class="log-action">{{item.action}}</text>
            <text class="log-time">{{item.formattedTime}}</text>
          </view>
          <view class="log-points {{item.type === 'gain' ? 'positive' : 'negative'}}">
            {{item.type === 'gain' ? '+' : ''}}{{item.points}}
          </view>
        </view>
      </block>

      <view class="empty-logs" wx:if="{{todayLogs.length === 0}}">
        <text class="empty-icon">📋</text>
        <text class="empty-text">今天还没有记录</text>
        <text class="empty-hint">完成习惯或兑换奖励后会显示在这里</text>
      </view>
    </scroll-view>
  </view>
</view>