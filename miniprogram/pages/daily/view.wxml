<!--pages/daily/view.wxml-->
<view class="container">
  <!-- 日期标题 -->
  <view class="header">
    <text class="date-title">{{currentDate}}</text>
  </view>

  <!-- 今日概览 -->
  <view class="overview-card">
    <view class="points-section">
      <text class="points-label">当前积分</text>
      <text class="points-value">{{totalPoints}}</text>
    </view>

    <view class="progress-section">
      <text class="progress-label">今日进度</text>
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{totalHabitsCount > 0 ? (completedHabitsCount / totalHabitsCount * 100) : 0}}%"></view>
      </view>
      <text class="progress-text">{{completedHabitsCount}}/{{totalHabitsCount}} 个习惯已完成</text>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <button class="action-btn habits-btn" bindtap="goToHabits">
      <text class="btn-icon">📝</text>
      <text class="btn-text">完成习惯</text>
    </button>
    <button class="action-btn rewards-btn" bindtap="goToRewards">
      <text class="btn-icon">🎁</text>
      <text class="btn-text">兑换奖励</text>
    </button>
    <button class="action-btn history-btn" bindtap="showHistoryDialog">
      <text class="btn-icon">📊</text>
      <text class="btn-text">历史记录</text>
    </button>
  </view>

  <!-- 今日记录 -->
  <view class="logs-section">
    <view class="section-title">
      <text>今日记录</text>
    </view>

    <scroll-view scroll-y class="logs-list">
      <block wx:for="{{todayLogs}}" wx:key="id">
        <view class="log-item {{item.type === 'gain' ? 'gain' : 'loss'}}">
          <view class="log-info">
            <text class="log-habit">{{item.habitName}}</text>
            <text class="log-action">{{item.action}}</text>
            <text class="log-time">{{item.formattedTime}}</text>
          </view>
          <view class="log-actions">
            <text class="log-points {{item.type === 'gain' ? 'positive' : 'negative'}}">
              {{item.type === 'gain' ? '+' : ''}}{{item.points}}
            </text>
            <!-- 只有完成习惯的记录才显示撤销按钮 -->
            <button
              class="btn undo-btn"
              bindtap="undoCompletion"
              data-log-id="{{item.id}}"
              data-habit-name="{{item.habitName}}"
              data-points="{{item.points}}"
              wx:if="{{item.action === '完成习惯' && item.type === 'gain'}}">
              撤销
            </button>
          </view>
        </view>
      </block>

      <view class="empty-logs" wx:if="{{todayLogs.length === 0}}">
        <text class="empty-icon">📋</text>
        <text class="empty-text">今天还没有记录</text>
        <text class="empty-hint">完成习惯或兑换奖励后会显示在这里</text>
      </view>
    </scroll-view>
  </view>

  <!-- 历史记录对话框 -->
  <view class="dialog-mask" wx:if="{{showHistoryDialog}}">
    <view class="history-dialog">
      <view class="dialog-header">
        <text>历史记录</text>
        <button class="close-btn" bindtap="hideHistoryDialog">×</button>
      </view>

      <scroll-view scroll-y class="history-content">
        <block wx:for="{{historyLogs}}" wx:key="date">
          <view class="history-date-section">
            <text class="history-date">{{item.date}}</text>
            <view class="history-logs">
              <block wx:for="{{item.logs}}" wx:key="id" wx:for-item="log">
                <view class="history-log-item {{log.type === 'gain' ? 'gain' : 'loss'}}">
                  <view class="log-info">
                    <text class="log-habit">{{log.habitName}}</text>
                    <text class="log-action">{{log.action}}</text>
                    <text class="log-time">{{log.formattedTime}}</text>
                  </view>
                  <text class="log-points {{log.type === 'gain' ? 'positive' : 'negative'}}">
                    {{log.type === 'gain' ? '+' : ''}}{{log.points}}
                  </text>
                </view>
              </block>
            </view>
          </view>
        </block>

        <view class="empty-history" wx:if="{{historyLogs.length === 0}}">
          <text class="empty-icon">📊</text>
          <text class="empty-text">暂无历史记录</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>