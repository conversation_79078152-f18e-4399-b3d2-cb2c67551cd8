/**index.wxss**/

page {
  background-color: #FFF9FA;
}

.container {
  padding: 40rpx 20rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF9FA 0%, #FFE4E1 100%);
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.header-icon {
  width: 60rpx;
  height: 60rpx;
  animation: twinkle 2s infinite;
}

@keyframes twinkle {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF69B4;
  text-shadow: 2rpx 2rpx 4rpx rgba(255, 105, 180, 0.3);
}

.points-card {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
  position: relative;
  overflow: hidden;
}

.points-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.points-label {
  display: block;
  color: white;
  font-size: 28rpx;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 1;
}

.points-value {
  display: block;
  color: white;
  font-size: 72rpx;
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.2);
  position: relative;
  z-index: 1;
}

.description {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  border: 2rpx dashed #FF69B4;
}

.description text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.nav-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, white, #FFF8DC);
  border-radius: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(255, 105, 180, 0.15);
  border: 3rpx solid #FFB6C1;
  transition: all 0.3s ease;
}

.nav-button:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 10rpx rgba(255, 105, 180, 0.2);
}

.nav-button image {
  width: 60rpx;
  height: 60rpx;
}

.nav-button text {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF69B4;
}

.habits-btn {
  background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
}

.daily-btn {
  background: linear-gradient(135deg, #E6F3FF, #F0F8FF);
}

.rewards-btn {
  background: linear-gradient(135deg, #F0FFF0, #F5FFFA);
}

.decoration {
  width: 200rpx;
  height: 200rpx;
  margin: 0 auto;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}
