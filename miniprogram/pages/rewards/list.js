// pages/rewards/list.js
Page({
  data: {
    rewards: [],
    totalPoints: 0,
    showAddDialog: false,
    showEditDialog: false,
    newRewardName: '',
    newRewardPoints: 10,
    editingReward: null,
    editRewardName: '',
    editRewardPoints: 10
  },

  onLoad() {
    this.loadRewards();
    this.loadTotalPoints();
  },

  onShow() {
    this.loadTotalPoints();
  },

  // 加载奖励列表
  loadRewards() {
    const rewards = wx.getStorageSync('rewards') || [];
    this.setData({ rewards });
  },

  // 加载总积分
  loadTotalPoints() {
    const totalPoints = wx.getStorageSync('totalPoints') || 0;
    this.setData({ totalPoints });
  },

  // 显示添加奖励对话框
  showAddRewardDialog() {
    this.setData({ showAddDialog: true });
  },

  // 隐藏添加奖励对话框
  hideAddRewardDialog() {
    this.setData({
      showAddDialog: false,
      newRewardName: '',
      newRewardPoints: 10
    });
  },

  // 显示编辑奖励对话框
  showEditRewardDialog(e) {
    const { id } = e.currentTarget.dataset;
    const reward = this.data.rewards.find(r => r.id === id);

    if (reward) {
      this.setData({
        showEditDialog: true,
        editingReward: reward,
        editRewardName: reward.name,
        editRewardPoints: reward.points
      });
    }
  },

  // 隐藏编辑奖励对话框
  hideEditRewardDialog() {
    this.setData({
      showEditDialog: false,
      editingReward: null,
      editRewardName: '',
      editRewardPoints: 10
    });
  },

  // 输入新奖励名称
  inputRewardName(e) {
    this.setData({ newRewardName: e.detail.value });
  },

  // 输入新奖励积分
  inputRewardPoints(e) {
    this.setData({ newRewardPoints: Number(e.detail.value) || 10 });
  },

  // 输入编辑奖励名称
  inputEditRewardName(e) {
    this.setData({ editRewardName: e.detail.value });
  },

  // 输入编辑奖励积分
  inputEditRewardPoints(e) {
    this.setData({ editRewardPoints: Number(e.detail.value) || 10 });
  },

  // 添加新奖励
  addReward() {
    if (!this.data.newRewardName.trim()) {
      wx.showToast({
        title: '请输入奖励名称',
        icon: 'none'
      });
      return;
    }

    const newReward = {
      id: Date.now(),
      name: this.data.newRewardName.trim(),
      points: this.data.newRewardPoints,
      redeemed: false
    };

    const rewards = [...this.data.rewards, newReward];
    wx.setStorageSync('rewards', rewards);
    this.setData({ rewards });
    this.hideAddRewardDialog();
  },

  // 保存编辑的奖励
  saveEditReward() {
    if (!this.data.editRewardName.trim()) {
      wx.showToast({
        title: '请输入奖励名称',
        icon: 'none'
      });
      return;
    }

    const rewards = this.data.rewards.map(reward => {
      if (reward.id === this.data.editingReward.id) {
        return {
          ...reward,
          name: this.data.editRewardName.trim(),
          points: this.data.editRewardPoints
        };
      }
      return reward;
    });

    wx.setStorageSync('rewards', rewards);
    this.setData({ rewards });
    this.hideEditRewardDialog();

    wx.showToast({
      title: '修改成功',
      icon: 'success'
    });
  },

  // 删除奖励
  deleteReward(e) {
    const { id } = e.currentTarget.dataset;
    const rewards = this.data.rewards.filter(reward => reward.id !== id);
    wx.setStorageSync('rewards', rewards);
    this.setData({ rewards });
  },

  // 兑换奖励
  redeemReward(e) {
    const { id } = e.currentTarget.dataset;
    const reward = this.data.rewards.find(r => r.id === id);

    if (!reward) return;

    if (this.data.totalPoints < reward.points) {
      wx.showToast({
        title: '积分不足，无法兑换',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认兑换',
      content: `确定要用${reward.points}积分兑换"${reward.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 扣除积分
          const newPoints = this.data.totalPoints - reward.points;
          wx.setStorageSync('totalPoints', newPoints);

          // 记录兑换日志
          this.logPointsChange(reward.name, -reward.points, '兑换奖励');

          // 记录兑换历史
          this.recordRedemption(reward);

          this.setData({ totalPoints: newPoints });

          // 庆祝兑换成功
          this.showRedemptionCelebration(reward);
        }
      }
    });
  },

  // 记录积分变化日志
  logPointsChange(rewardName, points, action) {
    const logs = wx.getStorageSync('pointsLogs') || [];
    const newLog = {
      id: Date.now(),
      date: new Date().toISOString(),
      habitName: rewardName,
      points,
      action,
      type: 'loss'
    };
    logs.unshift(newLog);
    if (logs.length > 100) {
      logs.splice(100);
    }
    wx.setStorageSync('pointsLogs', logs);
  },

  // 记录兑换历史
  recordRedemption(reward) {
    const redemptions = wx.getStorageSync('redemptions') || [];
    const newRedemption = {
      id: Date.now(),
      rewardName: reward.name,
      points: reward.points,
      date: new Date().toISOString()
    };
    redemptions.unshift(newRedemption);
    if (redemptions.length > 50) {
      redemptions.splice(50);
    }
    wx.setStorageSync('redemptions', redemptions);
  },

  // 显示兑换庆祝效果
  showRedemptionCelebration(reward) {
    const celebrations = [
      `🎉 恭喜！成功兑换"${reward.name}"！`,
      `🎊 太棒了！"${reward.name}"到手啦！`,
      `🌟 好厉害！获得了"${reward.name}"！`,
      `⭐ 真棒！"${reward.name}"是你的奖励！`
    ];

    const randomMessage = celebrations[Math.floor(Math.random() * celebrations.length)];

    wx.showModal({
      title: '兑换成功！',
      content: randomMessage,
      showCancel: false,
      confirmText: '太开心了！',
      confirmColor: '#FF69B4'
    });

    // 震动反馈
    wx.vibrateShort({
      type: 'heavy'
    });
  }
})