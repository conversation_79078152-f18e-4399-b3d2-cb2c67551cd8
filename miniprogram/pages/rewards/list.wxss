/* pages/rewards/list.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #FFF9FA;
}

.header {
  text-align: center;
  margin: 30rpx 0;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #FF69B4;
  display: block;
  margin-bottom: 20rpx;
}

.points-display {
  background-color: white;
  padding: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.1);
}

.points-label {
  font-size: 28rpx;
  color: #666;
}

.points-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-left: 10rpx;
}

.reward-list {
  height: 60vh;
}

.reward-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  margin-bottom: 20rpx;
  background-color: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.1);
  transition: all 0.3s ease;
}

.reward-item:active {
  transform: scale(0.98);
}

.reward-info {
  flex: 1;
}

.reward-name {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: bold;
}

.reward-points {
  display: block;
  color: #FF69B4;
  font-size: 28rpx;
}

.reward-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.redeem-btn {
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  border: none;
  font-weight: bold;
}

.redeem-btn.available {
  background-color: #4CAF50;
  color: white;
}

.redeem-btn.disabled {
  background-color: #ccc;
  color: #999;
}

.delete-btn {
  padding: 10rpx 20rpx;
  background-color: #ff4757;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-state text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.add-btn {
  width: 100%;
  padding: 25rpx;
  background-color: #FF69B4;
  color: white;
  border-radius: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 30rpx;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(255, 105, 180, 0.3);
}

.add-btn:active {
  transform: scale(0.98);
}

/* 对话框样式 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-content {
  width: 80%;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.dialog-header {
  padding: 30rpx;
  text-align: center;
  background-color: #FF69B4;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.dialog-body {
  padding: 30rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-group text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.input-group input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.dialog-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 25rpx;
  font-size: 28rpx;
  border: none;
  background-color: white;
}

.cancel-btn {
  color: #999;
  border-right: 1rpx solid #eee;
}

.confirm-btn {
  color: #FF69B4;
  font-weight: bold;
}