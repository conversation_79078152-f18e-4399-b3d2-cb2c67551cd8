/* pages/rewards/list.wxss */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #FFF9FA;
}

.header {
  margin: 20rpx 0;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.2);
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.points-display {
  text-align: right;
}

.points-label {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4rpx;
}

.points-value {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.reward-list {
  height: 60vh;
}

.reward-item {
  padding: 15rpx;
  margin-bottom: 10rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 105, 180, 0.08);
  transition: all 0.3s ease;
}

.reward-item:active {
  transform: scale(0.98);
}

.reward-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.reward-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.reward-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.reward-points {
  color: #FF69B4;
  font-size: 22rpx;
  font-weight: bold;
  background: rgba(255, 105, 180, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.reward-status {
  font-size: 20rpx;
  font-weight: bold;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.reward-status.available {
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.reward-status.unavailable {
  color: #FF6B6B;
  background: rgba(255, 107, 107, 0.1);
}

.reward-actions-row {
  display: flex;
  gap: 8rpx;
}

/* 统一按钮样式 */
.btn {
  border: none;
  border-radius: 16rpx;
  padding: 8rpx 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 60rpx;
  height: 56rpx;
  line-height: 40rpx;
}

.btn:active {
  transform: scale(0.95);
}

.redeem-btn {
  flex: 1.5;
  color: white;
  box-shadow: 0 1rpx 4rpx rgba(76, 175, 80, 0.3);
}

.redeem-btn.available {
  background: #4CAF50;
}

.redeem-btn.disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

.edit-btn {
  flex: 1;
  background: #2196F3;
  color: white;
}

.delete-btn {
  flex: 1;
  background: #FF6B81;
  color: white;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-state text {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.add-btn {
  position: fixed;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 80rpx;
  background-color: #FF69B4;
  color: white;
  border: none;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 3rpx 12rpx rgba(255, 105, 180, 0.3);
  transition: all 0.2s ease;
}

.add-btn:active {
  background-color: #FF1493;
  transform: translateX(-50%) scale(0.95);
}

/* 对话框样式 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-content {
  width: 80%;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.dialog-header {
  padding: 30rpx;
  text-align: center;
  background-color: #FF69B4;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.dialog-body {
  padding: 30rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-group text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.input-group input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.dialog-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 25rpx;
  font-size: 28rpx;
  border: none;
  background-color: white;
}

.cancel-btn {
  color: #999;
  border-right: 1rpx solid #eee;
}

.confirm-btn {
  color: #FF69B4;
  font-weight: bold;
}