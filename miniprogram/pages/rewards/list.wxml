<!--pages/rewards/list.wxml-->
<view class="container">
  <!-- 标题和积分显示 -->
  <view class="header">
    <view class="title-section">
      <text class="page-title">我的奖励</text>
      <view class="points-display">
        <text class="points-label">当前积分</text>
        <text class="points-value">{{totalPoints}}</text>
      </view>
    </view>
  </view>

  <!-- 奖励列表 -->
  <scroll-view scroll-y class="reward-list">
    <block wx:for="{{rewards}}" wx:key="id">
      <view class="reward-item">
        <!-- 奖励信息 -->
        <view class="reward-info-row">
          <text class="reward-name">{{item.name}}</text>
          <view class="reward-meta">
            <text class="reward-points">{{item.points}}分</text>
            <text class="reward-status {{totalPoints >= item.points ? 'available' : 'unavailable'}}">
              {{totalPoints >= item.points ? '可兑换' : '不足'}}
            </text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="reward-actions-row">
          <button
            class="btn redeem-btn {{totalPoints >= item.points ? 'available' : 'disabled'}}"
            bindtap="redeemReward"
            data-id="{{item.id}}"
            disabled="{{totalPoints < item.points}}">
            -{{item.points}}
          </button>

          <button class="btn edit-btn" bindtap="showEditRewardDialog" data-id="{{item.id}}">编辑</button>
          <button class="btn delete-btn" bindtap="deleteReward" data-id="{{item.id}}">删除</button>
        </view>
      </view>
    </block>

    <view class="empty-state" wx:if="{{rewards.length === 0}}">
      <text>还没有设置奖励哦～</text>
      <text>快来添加一些奖励吧！</text>
    </view>
  </scroll-view>

  <!-- 添加按钮 -->
  <button class="add-btn" bindtap="showAddRewardDialog">添加新奖励</button>

  <!-- 添加奖励对话框 -->
  <view class="dialog-mask" wx:if="{{showAddDialog}}">
    <view class="dialog-content">
      <view class="dialog-header">
        <text>添加新奖励</text>
      </view>
      <view class="dialog-body">
        <view class="input-group">
          <text>奖励名称:</text>
          <input placeholder="请输入奖励名称" bindinput="inputRewardName" value="{{newRewardName}}"/>
        </view>
        <view class="input-group">
          <text>所需积分:</text>
          <input type="number" placeholder="10" bindinput="inputRewardPoints" value="{{newRewardPoints}}"/>
        </view>
      </view>
      <view class="dialog-footer">
        <button class="cancel-btn" bindtap="hideAddRewardDialog">取消</button>
        <button class="confirm-btn" bindtap="addReward">确定</button>
      </view>
    </view>
  </view>

  <!-- 编辑奖励对话框 -->
  <view class="dialog-mask" wx:if="{{showEditDialog}}">
    <view class="dialog-content">
      <view class="dialog-header">
        <text>编辑奖励</text>
      </view>
      <view class="dialog-body">
        <view class="input-group">
          <text>奖励名称:</text>
          <input placeholder="请输入奖励名称" bindinput="inputEditRewardName" value="{{editRewardName}}"/>
        </view>
        <view class="input-group">
          <text>所需积分:</text>
          <input type="number" placeholder="10" bindinput="inputEditRewardPoints" value="{{editRewardPoints}}"/>
        </view>
      </view>
      <view class="dialog-footer">
        <button class="cancel-btn" bindtap="hideEditRewardDialog">取消</button>
        <button class="confirm-btn" bindtap="saveEditReward">保存</button>
      </view>
    </view>
  </view>
</view>